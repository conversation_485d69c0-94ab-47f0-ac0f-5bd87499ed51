#!/usr/bin/env python3
"""
Test script for loading the gated SAE from .pt file
"""

import torch
import json
from pathlib import Path
from dataclasses import dataclass
from typing import Union
import torch.nn as nn


@dataclass
class SaeConfig:
    """Simple SAE config for testing"""
    expansion_factor: int = 32
    normalize_decoder: bool = True
    num_latents: int = 0
    k: int = 32
    multi_topk: bool = False


class SimpleSae(nn.Module):
    """Simplified SAE class for testing"""
    
    def __init__(self, d_in: int, cfg: SaeConfig, device: Union[str, torch.device] = "cpu", decoder: bool = True):
        super().__init__()
        self.cfg = cfg
        self.d_in = d_in
        self.num_latents = cfg.num_latents or d_in * cfg.expansion_factor

        self.encoder = nn.Linear(d_in, self.num_latents, device=device)
        self.encoder.bias.data.zero_()

        self.W_dec = nn.Parameter(self.encoder.weight.data.clone()) if decoder else None
        self.b_dec = nn.Parameter(torch.zeros(d_in, device=device))

    @property
    def device(self):
        return self.encoder.weight.device

    @property
    def dtype(self):
        return self.encoder.weight.dtype


def load_from_pt_file(path: Union[Path, str], device: Union[str, torch.device] = "cpu", decoder: bool = True):
    """
    Load a gated SAE from a .pt file format.
    """
    path = Path(path)
    
    # Load the .pt file
    data = torch.load(path, map_location=device, weights_only=False)
    
    if "sae_state_dict" not in data or "config" not in data:
        raise ValueError(
            f"Invalid .pt file format. Expected 'sae_state_dict' and 'config' keys, got: {list(data.keys())}"
        )
    
    state_dict = data["sae_state_dict"]
    config = data["config"]
    
    # Extract d_in from the state dict or config
    if "d_in" in config and config["d_in"] is not None:
        d_in = config["d_in"]
    else:
        # Infer d_in from W_dec shape: W_dec is (d_sae, d_in)
        d_in = state_dict["W_dec"].shape[1]
    
    # Create SaeConfig from the loaded config
    expansion_factor = config.get("expansion_factor", 32)
    normalize_decoder = config.get("normalize_decoder", True)
    
    # Calculate num_latents from expansion_factor and d_in
    num_latents = d_in * expansion_factor
    
    cfg = SaeConfig(
        expansion_factor=expansion_factor,
        normalize_decoder=normalize_decoder,
        num_latents=num_latents,
        k=32,  # Default value
        multi_topk=False,  # Default value
    )
    
    # Create the SAE instance
    sae = SimpleSae(d_in, cfg, device=device, decoder=decoder)
    
    # Map the state dict parameters to the expected format
    mapped_state_dict = {}
    
    # Map encoder parameters - use W_enc as the encoder weight
    mapped_state_dict["encoder.weight"] = state_dict["W_enc"].T  # Transpose to match Linear layer format
    mapped_state_dict["encoder.bias"] = state_dict["b_enc"]
    
    # Map decoder parameters if requested
    if decoder:
        mapped_state_dict["W_dec"] = state_dict["W_dec"]
        mapped_state_dict["b_dec"] = state_dict["b_dec"]
    
    # Load the mapped state dict
    sae.load_state_dict(mapped_state_dict, strict=False)
    
    return sae


def main():
    """Test the loading function"""
    try:
        print("Loading SAE from .pt file...")
        sae = load_from_pt_file('llama_3_1_8b_layer16_gated_sae.pt', device='cpu')
        
        print("Successfully loaded SAE!")
        print(f"SAE device: {sae.device}")
        print(f"SAE dtype: {sae.dtype}")
        print(f"Input dimension: {sae.d_in}")
        print(f"Number of latents: {sae.num_latents}")
        print(f"Config: {sae.cfg}")
        
        # Test a forward pass
        print("\nTesting forward pass...")
        test_input = torch.randn(1, sae.d_in, device=sae.device, dtype=sae.dtype)
        
        # Test encoder
        encoded = sae.encoder(test_input)
        print(f"Encoded shape: {encoded.shape}")
        
        # Test decoder if available
        if sae.W_dec is not None:
            # Simple decode test - just matrix multiply
            decoded = encoded @ sae.W_dec + sae.b_dec
            print(f"Decoded shape: {decoded.shape}")
            print(f"Reconstruction error: {torch.norm(decoded - test_input).item():.6f}")
        
        print("\nAll tests passed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
