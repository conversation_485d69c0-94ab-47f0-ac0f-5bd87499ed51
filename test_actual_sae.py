#!/usr/bin/env python3
"""
Test script for the actual SAE implementation
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'itas'))

import torch
from pathlib import Path

# Import the specific modules we need
from itas.sae.config import SaeConfig
from itas.sae.sae import Sae

def main():
    """Test the actual SAE loading"""
    try:
        print("Testing actual SAE.load_from_pt_file method...")
        sae = Sae.load_from_pt_file('llama_3_1_8b_layer16_gated_sae.pt', device='cpu')
        
        print("Successfully loaded SAE!")
        print(f"SAE device: {sae.device}")
        print(f"SAE dtype: {sae.dtype}")
        print(f"Input dimension: {sae.d_in}")
        print(f"Number of latents: {sae.num_latents}")
        print(f"Config: {sae.cfg}")
        
        # Test a forward pass
        print("\nTesting forward pass...")
        test_input = torch.randn(1, sae.d_in, device=sae.device, dtype=sae.dtype)
        
        # Test pre_acts method
        pre_acts = sae.pre_acts(test_input)
        print(f"Pre-activations shape: {pre_acts.shape}")
        
        # Test encode method
        encoded = sae.encode(test_input)
        print(f"Encoded top_acts shape: {encoded.top_acts.shape}")
        print(f"Encoded top_indices shape: {encoded.top_indices.shape}")
        
        # Test decode method
        if sae.W_dec is not None:
            decoded = sae.decode(encoded.top_acts, encoded.top_indices)
            print(f"Decoded shape: {decoded.shape}")
            print(f"Reconstruction error: {torch.norm(decoded - test_input).item():.6f}")
        
        print("\nAll tests passed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
